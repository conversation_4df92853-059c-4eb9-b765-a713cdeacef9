# 驗證 Stable Diffusion 模型安裝
Write-Host "=== 驗證模型安裝狀況 ===" -ForegroundColor Green

$modelDir = "D:\Delta\tools\stable-diffusion-webui\models\Stable-diffusion"

Write-Host "`n檢查模型目錄: $modelDir" -ForegroundColor Cyan

# 預期的模型列表
$expectedModels = @(
    "Counterfeit-V3.0.safetensors",
    "anything-v5-PrtRE.safetensors", 
    "chilloutmix_NiPrunedFp32Fix.safetensors"
)

Write-Host "`n=== 當前已安裝的模型 ===" -ForegroundColor Magenta
$installedModels = Get-ChildItem $modelDir -Filter "*.safetensors"

if ($installedModels.Count -eq 0) {
    Write-Host "❌ 沒有找到任何 .safetensors 模型檔案" -ForegroundColor Red
} else {
    foreach ($model in $installedModels) {
        $sizeGB = [math]::Round($model.Length / 1GB, 2)
        $status = if ($expectedModels -contains $model.Name) { "✅" } else { "ℹ️" }
        Write-Host "$status $($model.Name) - ${sizeGB} GB" -ForegroundColor White
    }
}

Write-Host "`n=== 安裝狀況檢查 ===" -ForegroundColor Magenta
foreach ($expectedModel in $expectedModels) {
    $modelPath = Join-Path $modelDir $expectedModel
    if (Test-Path $modelPath) {
        $size = [math]::Round((Get-Item $modelPath).Length / 1GB, 2)
        Write-Host "✅ $expectedModel - ${size} GB" -ForegroundColor Green
    } else {
        Write-Host "❌ $expectedModel - 未安裝" -ForegroundColor Red
        
        # 提供下載建議
        switch ($expectedModel) {
            "anything-v5-PrtRE.safetensors" {
                Write-Host "   下載地址: https://civitai.com/models/9409/anything-or-v5-ink" -ForegroundColor Yellow
            }
            "chilloutmix_NiPrunedFp32Fix.safetensors" {
                Write-Host "   下載地址: https://civitai.com/models/6424/chilloutmix" -ForegroundColor Yellow
            }
        }
    }
}

$installedCount = ($expectedModels | Where-Object { Test-Path (Join-Path $modelDir $_) }).Count
$totalCount = $expectedModels.Count

Write-Host "`n=== 總結 ===" -ForegroundColor Cyan
Write-Host "已安裝: $installedCount/$totalCount 個推薦模型" -ForegroundColor $(if($installedCount -eq $totalCount){"Green"}else{"Yellow"})

if ($installedCount -lt $totalCount) {
    Write-Host "`n請下載缺少的模型並放置到:" -ForegroundColor Yellow
    Write-Host "$modelDir" -ForegroundColor White
} else {
    Write-Host "`n🎉 所有推薦模型已安裝完成！" -ForegroundColor Green
    Write-Host "請重啟 Stable Diffusion WebUI 以載入新模型" -ForegroundColor Cyan
}
