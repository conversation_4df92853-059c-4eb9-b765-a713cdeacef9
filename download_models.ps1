# Stable Diffusion 模型下載腳本
# 為隔牆的溫柔遊戲項目精選的NSFW動漫模型

Write-Host "=== 開始下載適合成人視覺小說的Stable Diffusion模型 ===" -ForegroundColor Green

# 設定目標目錄
$modelDir = "D:\Delta\tools\stable-diffusion-webui\models\Stable-diffusion"
$tempDir = "$env:TEMP\sd_models"

# 創建臨時下載目錄
if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force
    Write-Host "創建臨時目錄: $tempDir" -ForegroundColor Yellow
}

# 模型下載列表 (精選適合您項目的模型)
$models = @(
    @{
        Name = "Anything V5"
        Description = "最佳動漫風格基礎模型，角色一致性優秀"
        URL = "https://huggingface.co/stablediffusionapi/anything-v5/resolve/main/anything-v5-PrtRE.safetensors"
        FileName = "anything-v5-PrtRE.safetensors"
        Priority = 1
    },
    @{
        Name = "ChilloutMix"
        Description = "寫實動漫混合，NSFW內容表現優秀"
        URL = "https://huggingface.co/TASUKU2023/Chilloutmix/resolve/main/chilloutmix_NiPrunedFp32Fix.safetensors"
        FileName = "chilloutmix_NiPrunedFp32Fix.safetensors"
        Priority = 2
    },
    @{
        Name = "AbyssOrangeMix3"
        Description = "專為角色一致性優化的動漫模型"
        URL = "https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix3/AOM3A3_orangemixs.safetensors"
        FileName = "AOM3A3_orangemixs.safetensors"
        Priority = 3
    }
)

# 下載函數
function Download-Model {
    param(
        [string]$URL,
        [string]$FileName,
        [string]$Description
    )

    $outputPath = Join-Path $tempDir $FileName
    $finalPath = Join-Path $modelDir $FileName

    # 檢查是否已存在
    if (Test-Path $finalPath) {
        Write-Host "模型已存在: $FileName" -ForegroundColor Cyan
        return $true
    }

    Write-Host "正在下載: $Description" -ForegroundColor Yellow
    Write-Host "URL: $URL" -ForegroundColor Gray

    try {
        # 使用 Invoke-WebRequest 下載
        $ProgressPreference = 'Continue'
        Invoke-WebRequest -Uri $URL -OutFile $outputPath -UseBasicParsing

        # 移動到最終位置
        Move-Item $outputPath $finalPath -Force
        Write-Host "✓ 下載完成: $FileName" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ 下載失敗: $FileName" -ForegroundColor Red
        Write-Host "錯誤: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 開始下載
$successCount = 0
$totalCount = $models.Count

Write-Host "`n開始下載 $totalCount 個模型..." -ForegroundColor Cyan

foreach ($model in $models | Sort-Object Priority) {
    Write-Host "`n--- 下載模型 $($model.Priority)/$totalCount ---" -ForegroundColor Magenta
    Write-Host "名稱: $($model.Name)" -ForegroundColor White
    Write-Host "描述: $($model.Description)" -ForegroundColor Gray
    
    if (Download-Model -URL $model.URL -FileName $model.FileName -Description $model.Name) {
        $successCount++
    }
    
    # 添加延遲避免服務器限制
    Start-Sleep -Seconds 2
}

# 清理臨時目錄
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
    Write-Host "`n臨時目錄已清理" -ForegroundColor Yellow
}

# 總結
Write-Host "`n=== 下載完成總結 ===" -ForegroundColor Green
Write-Host "成功下載: $successCount/$totalCount 個模型" -ForegroundColor $(if($successCount -eq $totalCount){"Green"}else{"Yellow"})

if ($successCount -gt 0) {
    Write-Host "`n已下載的模型位於: $modelDir" -ForegroundColor Cyan
    Write-Host "請重啟 Stable Diffusion WebUI 以載入新模型" -ForegroundColor Yellow
}

# 顯示推薦使用順序
Write-Host "`n=== 推薦使用順序 ===" -ForegroundColor Magenta
Write-Host "1. Anything V5 - 用於女主角美咲的基礎立繪" -ForegroundColor White
Write-Host "2. ChilloutMix - 用於成人內容和親密場景" -ForegroundColor White  
Write-Host "3. AbyssOrangeMix3 - 用於確保角色一致性" -ForegroundColor White

Write-Host "`n按任意鍵繼續..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
