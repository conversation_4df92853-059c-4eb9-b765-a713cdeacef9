## 螢幕界面定義
## 隔牆的溫柔 - 使用者介面

## 主選單螢幕
screen main_menu():
    tag menu
    
    add gui.main_menu_background
    
    frame:
        style "main_menu_frame"
        
        has vbox
        
        textbutton _("開始遊戲") action Start()
        textbutton _("載入遊戲") action ShowMenu("load")
        textbutton _("設定") action ShowMenu("preferences")
        textbutton _("關於") action ShowMenu("about")
        textbutton _("結束遊戲") action Quit(confirm=not main_menu)

## 遊戲選單螢幕
screen game_menu(title, scroll=None, yinitial=0.0):
    style_prefix "game_menu"
    
    if main_menu:
        add gui.main_menu_background
    else:
        add gui.game_menu_background
    
    frame:
        style "game_menu_outer_frame"
        
        has hbox
        
        frame:
            style "game_menu_navigation_frame"
            
        frame:
            style "game_menu_content_frame"
            
            if scroll == "viewport":
                viewport:
                    yinitial yinitial
                    scrollbars "vertical"
                    mousewheel True
                    draggable True
                    pagekeys True
                    
                    side_yfill True
                    
                    has vbox
                    transclude
            
            elif scroll == "vpgrid":
                vpgrid:
                    cols 1
                    yinitial yinitial
                    scrollbars "vertical"
                    mousewheel True
                    draggable True
                    pagekeys True
                    
                    side_yfill True
                    
                    transclude
            
            else:
                transclude
    
    use navigation
    
    textbutton _("返回"):
        style "return_button"
        action Return()
    
    label title

## 導航選單
screen navigation():
    vbox:
        style_prefix "navigation"
        
        xpos gui.navigation_xpos
        yalign 0.5
        
        spacing gui.navigation_spacing
        
        if main_menu:
            textbutton _("開始遊戲") action Start()
        else:
            textbutton _("歷史") action ShowMenu("history")
            textbutton _("存檔") action ShowMenu("save")
        
        textbutton _("載入遊戲") action ShowMenu("load")
        textbutton _("設定") action ShowMenu("preferences")
        
        if _in_replay:
            textbutton _("結束重播") action EndReplay(confirm=True)
        elif not main_menu:
            textbutton _("主選單") action MainMenu()
        else:
            textbutton _("關於") action ShowMenu("about")
        
        if renpy.variant("pc") or (renpy.variant("web") and not renpy.variant("mobile")):
            textbutton _("結束遊戲") action Quit(confirm=not main_menu)

## 對話螢幕
screen say(who, what):
    style_prefix "say"
    
    window:
        id "window"
        
        if who is not None:
            window:
                id "namebox"
                style "namebox"
                text who id "who"
        
        text what id "what"
    
    ## 快速選單
    if not renpy.variant("small"):
        add SideImage() xalign 0.0 yalign 1.0

## 選擇螢幕
screen choice(items):
    style_prefix "choice"
    
    vbox:
        for i in items:
            textbutton i.caption action i.action

## 快速選單螢幕
screen quick_menu():
    variant "medium"
    
    zorder 100
    
    if quick_menu:
        hbox:
            style_prefix "quick"
            
            xalign 0.5
            yalign 1.0
            
            textbutton _("返回") action Rollback()
            textbutton _("歷史") action ShowMenu('history')
            textbutton _("跳過") action Skip() alternate Skip(fast=True, confirm=True)
            textbutton _("自動") action Preference("auto-forward", "toggle")
            textbutton _("存檔") action ShowMenu('save')
            textbutton _("快存") action QuickSave()
            textbutton _("快讀") action QuickLoad()
            textbutton _("設定") action ShowMenu('preferences')

## 設定螢幕
screen preferences():
    tag menu
    
    use game_menu(_("設定"), scroll="viewport"):
        vbox:
            hbox:
                box_wrap True
                
                if renpy.variant("pc") or renpy.variant("web"):
                    vbox:
                        style_prefix "radio"
                        label _("顯示")
                        textbutton _("視窗") action Preference("display", "window")
                        textbutton _("全螢幕") action Preference("display", "fullscreen")
                
                vbox:
                    style_prefix "check"
                    label _("跳過")
                    textbutton _("未讀文字") action Preference("skip", "toggle")
                    textbutton _("選擇後") action Preference("after choices", "toggle")
                    textbutton _("轉場") action InvertSelected(Preference("transitions", "toggle"))
                
                ## 音量設定
                vbox:
                    label _("音量")
                    
                    hbox:
                        style_prefix "slider"
                        
                        vbox:
                            label _("主音量")
                            hbox:
                                bar value Preference("master volume")
                            
                            label _("音樂")
                            hbox:
                                bar value Preference("music volume")
                            
                            label _("音效")
                            hbox:
                                bar value Preference("sound volume")
                            
                            if config.has_voice:
                                label _("語音")
                                hbox:
                                    bar value Preference("voice volume")
                                    
                                    if config.has_voice:
                                        textbutton _("語音測試") action Play("voice", "voice/test.ogg")

## 關於螢幕
screen about():
    tag menu
    
    use game_menu(_("關於"), scroll="viewport"):
        style_prefix "about"
        
        vbox:
            label "[config.name!t]"
            text _("版本 [config.version!t]\n")
            
            if gui.about:
                text "[gui.about!t]\n"
            
            text _("使用 {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only] 製作。\n\n[renpy.license!t]")

## 存檔/載入螢幕
screen save():
    tag menu
    
    use file_slots(_("存檔"))

screen load():
    tag menu
    
    use file_slots(_("載入遊戲"))

screen file_slots(title):
    default page_name_value = FilePageNameInputValue(pattern=_("第 {} 頁"), auto=_("自動存檔"), quick=_("快速存檔"))
    
    use game_menu(title):
        fixed:
            order_reverse True
            
            button:
                style "page_label"
                
                key_events True
                xalign 0.5
                action page_name_value.Toggle()
                
                input:
                    style "page_label_text"
                    value page_name_value
            
            grid gui.file_slot_cols gui.file_slot_rows:
                style_prefix "slot"
                
                transpose True
                xalign 0.5
                
                for i in range(gui.file_slot_cols * gui.file_slot_rows):
                    $ slot = i + 1
                    
                    button:
                        action FileAction(slot)
                        
                        has vbox
                        
                        add FileScreenshot(slot) xalign 0.5
                        
                        text FileTime(slot, format=_("{#file_time}%A, %B %d %Y, %H:%M"), empty=_("空存檔位")):
                            style "slot_time_text"
                        
                        text FileSaveName(slot):
                            style "slot_name_text"
                        
                        key "save_delete" action FileDelete(slot)
            
            hbox:
                style_prefix "page"
                
                xalign 0.5
                yalign 1.0
                
                spacing gui.page_spacing
                
                textbutton _("<") action FilePagePrevious()
                
                if config.has_autosave:
                    textbutton _("{#auto_page}A") action FilePage("auto")
                
                if config.has_quicksave:
                    textbutton _("{#quick_page}Q") action FilePage("quick")
                
                for page in range(1, 10):
                    textbutton "[page]" action FilePage(page)
                
                textbutton _(">") action FilePageNext()

## 歷史螢幕
screen history():
    tag menu
    
    predict False
    
    use game_menu(_("歷史"), scroll=("vpgrid" if gui.history_height else "viewport"), yinitial=1.0):
        style_prefix "history"
        
        for h in _history_list:
            window:
                has fixed:
                    yfit True
                
                if h.who:
                    label h.who:
                        style "history_name"
                        substitute False
                        
                        if "color" in h.who_args:
                            text_color h.who_args["color"]
                
                $ what = renpy.filter_text_tags(h.what, allow=gui.history_allow_tags)
                text what:
                    substitute False
        
        if not _history_list:
            label _("對話歷史是空的。")

## 成人內容確認螢幕
screen adult_warning():
    modal True
    
    add "#000"
    
    frame:
        style_prefix "confirm"
        
        xalign .5
        yalign .5
        
        vbox:
            xalign .5
            spacing 45
            
            label _("{size=+10}{color=#ff6666}成人內容警告{/color}{/size}"):
                style "confirm_prompt"
                xalign 0.5
            
            text _("本遊戲包含成人內容，僅適合18歲以上玩家。\n\n請確認您已年滿18歲。"):
                style "confirm_prompt"
                xalign 0.5
            
            hbox:
                xalign 0.5
                spacing 150
                
                textbutton _("我已年滿18歲") action Return(True)
                textbutton _("我未滿18歲") action Quit()
