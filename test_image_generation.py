#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美咲角色立繪測試生成腳本
用於測試下載的模型並生成基礎立繪
"""

import json
import time
from pathlib import Path

# 測試用的提示詞配置
TEST_PROMPTS = {
    "misaki_basic": {
        "prompt": "mature woman, 33 years old, beautiful japanese woman, G-cup large breasts, medium-long brown hair, coffee colored hair, natural body hair, armpit hair visible, gentle expression, lonely eyes, realistic proportions, detailed anatomy, soft skin, calm expression, gentle smile, home atmosphere",
        "negative_prompt": "child, young, teenager, small breasts, shaved, hairless body, unrealistic proportions, cartoon, extra limbs, blurry details, low quality, bad anatomy",
        "width": 1024,
        "height": 1024,
        "steps": 25,
        "cfg_scale": 8,
        "sampler_name": "DPM++ 2M Karras",
        "seed": 123456789
    },
    
    "misaki_shy": {
        "prompt": "mature woman, 33 years old, beautiful japanese woman, G-cup large breasts, medium-long brown hair, coffee colored hair, natural body hair, blushing, embarrassed, looking away, shy smile, realistic proportions, detailed anatomy, soft skin",
        "negative_prompt": "child, young, teenager, small breasts, shaved, hairless body, unrealistic proportions, cartoon, extra limbs, blurry details, low quality, bad anatomy",
        "width": 1024,
        "height": 1024,
        "steps": 25,
        "cfg_scale": 8,
        "sampler_name": "DPM++ 2M Karras",
        "seed": 123456789
    },
    
    "misaki_nsfw": {
        "prompt": "mature woman, 33 years old, beautiful japanese woman, G-cup large breasts, medium-long brown hair, coffee colored hair, natural body hair, armpit hair, pubic hair, lustful expression, half-closed eyes, sensual, bedroom, dim lighting, intimate atmosphere",
        "negative_prompt": "child, young, teenager, small breasts, shaved, hairless body, unrealistic proportions, cartoon, extra limbs, blurry details, low quality, bad anatomy",
        "width": 1024,
        "height": 1024,
        "steps": 30,
        "cfg_scale": 10,
        "sampler_name": "DPM++ 2M Karras",
        "seed": 123456789
    }
}

def check_models():
    """檢查模型是否已下載"""
    model_dir = Path("D:/Delta/tools/stable-diffusion-webui/models/Stable-diffusion")
    
    expected_models = [
        "Counterfeit-V3.0.safetensors",
        "anything-v5-PrtRE.safetensors", 
        "chilloutmix_NiPrunedFp32Fix.safetensors"
    ]
    
    print("=== 檢查模型狀況 ===")
    installed_models = []
    
    for model in expected_models:
        model_path = model_dir / model
        if model_path.exists():
            size_gb = model_path.stat().st_size / (1024**3)
            print(f"✅ {model} - {size_gb:.2f} GB")
            installed_models.append(model)
        else:
            print(f"❌ {model} - 未找到")
    
    print(f"\n已安裝: {len(installed_models)}/{len(expected_models)} 個模型")
    return installed_models

def generate_test_image(prompt_name, model_name="anything-v5-PrtRE"):
    """生成測試圖片"""
    if prompt_name not in TEST_PROMPTS:
        print(f"❌ 未找到提示詞配置: {prompt_name}")
        return False
    
    config = TEST_PROMPTS[prompt_name]
    
    # 構建API請求
    payload = {
        "prompt": config["prompt"],
        "negative_prompt": config["negative_prompt"],
        "width": config["width"],
        "height": config["height"],
        "steps": config["steps"],
        "cfg_scale": config["cfg_scale"],
        "sampler_name": config["sampler_name"],
        "seed": config["seed"],
        "override_settings": {
            "sd_model_checkpoint": model_name
        }
    }
    
    print(f"\n=== 生成測試圖片: {prompt_name} ===")
    print(f"模型: {model_name}")
    print(f"提示詞: {config['prompt'][:100]}...")
    print(f"參數: {config['width']}x{config['height']}, {config['steps']} steps, CFG {config['cfg_scale']}")
    
    # 這裡應該調用Stable Diffusion API
    # 由於我們還沒有啟動WebUI，先返回配置信息
    return payload

def main():
    """主函數"""
    print("🎨 美咲角色立繪測試生成器")
    print("=" * 50)
    
    # 檢查模型
    installed_models = check_models()
    
    if len(installed_models) == 0:
        print("\n❌ 沒有找到任何模型，請先下載模型")
        return
    
    print(f"\n✅ 找到 {len(installed_models)} 個模型，可以開始測試")
    
    # 推薦測試順序
    test_sequence = [
        ("misaki_basic", "anything-v5-PrtRE" if "anything-v5-PrtRE.safetensors" in installed_models else "Counterfeit-V3.0"),
        ("misaki_shy", "anything-v5-PrtRE" if "anything-v5-PrtRE.safetensors" in installed_models else "Counterfeit-V3.0"),
        ("misaki_nsfw", "chilloutmix_NiPrunedFp32Fix" if "chilloutmix_NiPrunedFp32Fix.safetensors" in installed_models else "Counterfeit-V3.0")
    ]
    
    print("\n=== 推薦測試順序 ===")
    for i, (prompt_name, model) in enumerate(test_sequence, 1):
        print(f"{i}. {prompt_name} (使用 {model})")
    
    # 生成測試配置
    print("\n=== 生成測試配置 ===")
    test_configs = {}
    
    for prompt_name, model in test_sequence:
        config = generate_test_image(prompt_name, model)
        if config:
            test_configs[prompt_name] = config
    
    # 保存測試配置到文件
    config_file = Path("test_generation_configs.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(test_configs, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 測試配置已保存到: {config_file}")
    
    print("\n=== 下一步操作 ===")
    print("1. 確保 Stable Diffusion WebUI 正在運行 (http://127.0.0.1:7860)")
    print("2. 在WebUI中選擇對應的模型")
    print("3. 複製上述提示詞進行測試生成")
    print("4. 檢查生成結果的角色一致性")
    
    print("\n🎯 測試重點:")
    print("- 美咲的基本外貌特徵是否正確")
    print("- 不同表情之間的一致性")
    print("- 成人內容的品質和適宜性")
    print("- 整體風格是否符合遊戲需求")

if __name__ == "__main__":
    main()
