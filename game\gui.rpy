## GUI設定檔案
## 隔牆的溫柔 - UI介面設計

## 基本顏色設定
define gui.accent_color = '#ff6666'
define gui.idle_color = '#888888'
define gui.idle_small_color = '#aaaaaa'
define gui.hover_color = '#ff8888'
define gui.selected_color = '#ffffff'
define gui.insensitive_color = '#8888887f'
define gui.muted_color = '#512d29'
define gui.hover_muted_color = '#7a453f'
define gui.text_color = '#ffffff'
define gui.interface_text_color = '#ffffff'

## 字體設定
define gui.text_font = "DejaVuSans.ttf"
define gui.name_text_font = "DejaVuSans-Bold.ttf"
define gui.interface_text_font = "DejaVuSans.ttf"
define gui.default_font = "DejaVuSans.ttf"

## 字體大小
define gui.text_size = 33
define gui.name_text_size = 45
define gui.interface_text_size = 33
define gui.button_text_size = 34
define gui.choice_button_text_size = 30
define gui.label_text_size = 36
define gui.notify_text_size = 24
define gui.title_text_size = 75

## 對話框設定
define gui.textbox_height = 278
define gui.textbox_yalign = 1.0

## 名字框設定
define gui.name_xpos = 360
define gui.name_ypos = 0
define gui.name_xalign = 0.0
define gui.namebox_width = None
define gui.namebox_height = None
define gui.namebox_borders = Borders(5, 5, 5, 5)
define gui.namebox_tile = False

## 對話文字設定
define gui.dialogue_xpos = 402
define gui.dialogue_ypos = 75
define gui.dialogue_width = 1116
define gui.dialogue_text_xalign = 0.0

## 按鈕設定
define gui.button_width = None
define gui.button_height = None
define gui.button_borders = Borders(6, 6, 6, 6)
define gui.button_tile = False
define gui.button_text_font = gui.interface_text_font
define gui.button_text_size = gui.interface_text_size
define gui.button_text_idle_color = gui.idle_color
define gui.button_text_hover_color = gui.hover_color
define gui.button_text_selected_color = gui.selected_color
define gui.button_text_insensitive_color = gui.insensitive_color
define gui.button_text_xalign = 0.0

## 選擇按鈕設定
define gui.choice_button_width = 1185
define gui.choice_button_height = None
define gui.choice_button_tile = False
define gui.choice_button_borders = Borders(150, 8, 150, 8)
define gui.choice_button_text_font = gui.text_font
define gui.choice_button_text_size = gui.choice_button_text_size
define gui.choice_button_text_xalign = 0.5
define gui.choice_button_text_idle_color = '#cccccc'
define gui.choice_button_text_hover_color = "#ffffff"
define gui.choice_button_text_insensitive_color = '#444444'

## 快速選單設定
define gui.quick_button_borders = Borders(10, 10, 10, 10)
define gui.quick_button_text_size = 21
define gui.quick_button_text_idle_color = gui.idle_small_color
define gui.quick_button_text_selected_color = gui.accent_color

## 選單設定
define gui.page_button_borders = Borders(15, 6, 15, 6)
define gui.slot_button_width = 414
define gui.slot_button_height = 309
define gui.slot_button_borders = Borders(15, 15, 15, 15)
define gui.slot_button_text_size = 21
define gui.slot_button_text_xalign = 0.5
define gui.slot_button_text_idle_color = gui.idle_small_color
define gui.slot_button_text_selected_idle_color = gui.selected_color
define gui.slot_button_text_selected_hover_color = gui.hover_color

## 標題選單設定
define gui.main_menu_background = "gui/main_menu.png"
define gui.main_menu_text_xalign = 1.0

## 遊戲選單設定
define gui.game_menu_background = "gui/game_menu.png"

## 對話歷史設定
define gui.history_height = 210
define gui.history_name_xpos = 233
define gui.history_name_ypos = 0
define gui.history_name_xalign = 1.0
define gui.history_name_yalign = 0.5
define gui.history_name_width = 213
define gui.history_name_height = 210
define gui.history_text_xpos = 255
define gui.history_text_ypos = 3
define gui.history_text_width = 1110
define gui.history_text_height = 210
define gui.history_text_xalign = 0.0
define gui.history_text_yalign = 0.0

## NVL模式設定
define gui.nvl_borders = Borders(0, 15, 0, 30)
define gui.nvl_list_length = 6
define gui.nvl_height = 173
define gui.nvl_name_xpos = 645
define gui.nvl_name_ypos = 0
define gui.nvl_name_xalign = 1.0
define gui.nvl_name_yalign = 1.0
define gui.nvl_name_width = 225
define gui.nvl_name_height = 173
define gui.nvl_text_xpos = 675
define gui.nvl_text_ypos = 12
define gui.nvl_text_width = 885
define gui.nvl_text_height = 173
define gui.nvl_text_xalign = 0.0
define gui.nvl_text_yalign = 0.0
define gui.nvl_thought_xpos = 360
define gui.nvl_thought_ypos = 0
define gui.nvl_thought_width = 1170
define gui.nvl_thought_height = 173
define gui.nvl_thought_xalign = 0.0
define gui.nvl_thought_yalign = 0.0
define gui.nvl_button_xpos = 675
define gui.nvl_button_xalign = 0.0

## 本地化設定
define gui.language = "unicode"

init python:
    ## 自定義GUI函數
    def gui_color(color):
        return Color(color)
    
    ## 成人內容UI調整
    config.overlay_functions.append(renpy.show_screen)
    
    ## 快捷鍵提示
    config.help = "F1"
    
    ## 自動存檔設定
    config.has_autosave = True
    config.autosave_frequency = 200
    
    ## 跳過設定
    config.allow_skipping = True
    config.fast_skipping = True
