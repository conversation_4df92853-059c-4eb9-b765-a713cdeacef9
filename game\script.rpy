## 主劇本檔案
## 隔牆的溫柔 - Tenderness Beyond the Wall

## 角色定義
define m = Character("美咲", color="#c8ffc8")
define p = Character("你", color="#c8c8ff")
define narrator = Character(None, kind=nvl)

## 變數初始化
default affection = 0
default chapter = 1
default ending_route = ""

## 遊戲開始
label start:
    
    ## 成人內容警告
    scene black
    with fade
    
    centered "{size=+10}{color=#ff6666}成人內容警告{/color}{/size}\n\n本遊戲包含成人內容，僅適合18歲以上玩家。\n\n請確認您已年滿18歲。"
    
    menu:
        "我已年滿18歲，繼續遊戲":
            pass
        "我未滿18歲，退出遊戲":
            return
    
    ## 遊戲標題畫面
    scene black
    with fade
    
    centered "{size=+20}{color=#ffc8c8}隔牆的溫柔{/color}{/size}\n{size=+5}Tenderness Beyond the Wall{/size}\n\n{size=-5}製作：紫暮工坊 Twilight Purple Studio{/size}"
    
    pause 3.0
    
    ## 開始第一章
    jump chapter1

## 第一章：深夜的邂逅
label chapter1:
    
    $ chapter = 1
    
    scene black
    with fade
    
    centered "{size=+10}第一章：深夜的邂逅{/size}"
    
    pause 2.0
    
    ## 場景：男主角公寓臥室
    scene bg_male_bedroom
    with fade
    
    "深夜2點15分。"
    
    "我被一陣微弱的聲音驚醒。"
    
    "起初以為是電視的聲音，但仔細聆聽後..."
    
    "那似乎是...女性的呻吟聲？"
    
    "聲音從隔壁傳來，時斷時續，帶著某種壓抑的情感。"
    
    p "這個時間...隔壁在做什麼？"
    
    p "聽起來像是...不，應該不會吧。"
    
    p "但是這聲音..."
    
    ## 第一個重要選擇
    menu:
        "敲門關心":
            $ affection += 2
            jump choice1_knock
        
        "默默聆聽":
            $ affection += 1
            jump choice1_listen
        
        "戴上耳機睡覺":
            $ affection -= 1
            jump choice1_ignore

## 選擇分支：敲門關心
label choice1_knock:
    
    "我決定去關心一下鄰居的狀況。"
    
    "也許她真的遇到了什麼困難。"
    
    scene bg_hallway
    with fade
    
    "我輕輕走到隔壁門前，猶豫了一下。"
    
    "然後輕敲了幾下門。"
    
    "咚咚咚..."
    
    "聲音突然停止了。"
    
    "過了一會兒，門內傳來慌張的腳步聲。"
    
    "門開了一條縫。"
    
    show misaki embarrassed
    with dissolve
    
    "一個美麗的女性出現在門後。"
    
    "她穿著薄薄的睡衣，臉頰還帶著紅暈，頭髮微亂。"
    
    "這就是我的鄰居...美咲。"
    
    m "啊...不好意思，這麼晚了..."
    
    p "不好意思這麼晚打擾，我聽到一些聲音，擔心妳是不是不舒服？"
    
    show misaki shy
    with dissolve
    
    m "我...我只是...看電視看得太入神了。"
    
    m "謝謝你的關心。"
    
    "她的聲音有些顫抖，眼神閃躲著不敢直視我。"
    
    "但我能感受到她眼中的溫柔。"
    
    p "如果有什麼需要幫忙的，隨時可以找我。"
    
    show misaki normal
    with dissolve
    
    m "謝謝...真的很謝謝你。"
    
    "她輕輕關上了門。"
    
    "但我能感覺到，這次相遇改變了什麼。"
    
    jump chapter1_end

## 選擇分支：默默聆聽
label choice1_listen:
    
    "我決定靜靜聆聽。"
    
    "也許...這是她的私人時間。"
    
    "聲音繼續傳來，帶著某種孤獨和渴望。"
    
    "我能感受到隔牆另一邊的寂寞。"
    
    "漸漸地，聲音平息了。"
    
    "我也重新躺回床上，但已經無法入睡。"
    
    "腦海中不斷想著那個聲音的主人。"
    
    jump chapter1_end

## 選擇分支：戴上耳機
label choice1_ignore:
    
    "我決定不去理會。"
    
    "每個人都有自己的隱私。"
    
    "我戴上耳機，試圖重新入睡。"
    
    "但那個聲音似乎已經深深印在我的記憶中。"
    
    jump chapter1_end

## 第一章結束
label chapter1_end:
    
    scene black
    with fade
    
    "第一章結束"
    
    "好感度：[affection]"
    
    pause 2.0
    
    ## 繼續到第二章
    jump chapter2

## 第二章：逐漸親近
label chapter2:
    
    $ chapter = 2
    
    scene black
    with fade
    
    centered "{size=+10}第二章：逐漸親近{/size}"
    
    pause 2.0
    
    "一週後..."
    
    scene bg_hallway
    with fade
    
    "我和美咲的互動開始增加。"
    
    if affection >= 2:
        "自從那晚的相遇後，她對我似乎更加信任。"
    else:
        "雖然我們之前沒有直接接觸，但命運讓我們再次相遇。"
    
    show misaki normal
    with dissolve
    
    "今天我看到她在搬重物。"
    
    p "需要幫忙嗎？"
    
    show misaki shy
    with dissolve
    
    m "啊...如果不麻煩的話..."
    
    "我幫她搬運了一些箱子。"
    
    "在這個過程中，我們開始了更多的對話。"
    
    show misaki normal
    with dissolve
    
    m "真的很謝謝你。自從我先生過世後，很少有人這樣幫助我。"
    
    p "鄰居互相幫忙是應該的，而且...妳一個人住一定很辛苦。"
    
    show misaki sad
    with dissolve
    
    m "是啊...有時候真的很寂寞。"
    
    "她的眼中閃過一絲哀傷。"
    
    "但很快又恢復了溫和的笑容。"
    
    ## 第二個重要選擇
    menu:
        "主動邀請她吃晚餐":
            $ affection += 3
            jump choice2_dinner
        
        "暗示可以隨時找自己幫忙":
            $ affection += 2
            jump choice2_help
        
        "保持現狀，不多說什麼":
            $ affection += 1
            jump choice2_neutral

## 選擇分支：邀請晚餐
label choice2_dinner:

    p "如果妳不介意的話...要不要一起吃個晚餐？"

    p "我知道一家不錯的餐廳，或者我可以做點簡單的料理。"

    show misaki surprised
    with dissolve

    m "啊...這樣會不會太麻煩你了？"

    "她的臉頰微微泛紅，似乎有些意外但也有些期待。"

    p "一點也不麻煩，我也很久沒有和人一起用餐了。"

    show misaki shy
    with dissolve

    m "那...那就麻煩你了。"

    "她的聲音很輕，但我能感受到她內心的喜悅。"

    scene bg_restaurant
    with fade

    "我們來到了附近一家安靜的小餐廳。"

    show misaki normal
    with dissolve

    "在用餐過程中，美咲逐漸放鬆下來。"

    m "很久沒有這樣和人聊天了...感覺很溫暖。"

    p "妳平常都一個人嗎？"

    show misaki sad
    with dissolve

    m "嗯...自從先生過世後，朋友們也漸漸疏遠了。"

    m "大概是不知道該怎麼面對寡婦吧。"

    "她苦笑著，眼中帶著無奈。"

    p "那一定很孤單。"

    show misaki gentle
    with dissolve

    m "是啊...特別是夜晚的時候。"

    "她的話讓我想起了那個深夜聽到的聲音。"

    "我能理解她內心的寂寞和渴望。"

    p "如果妳願意，我們可以經常這樣聊天。"

    show misaki happy
    with dissolve

    m "真的嗎？那就太好了。"

    "她的笑容如花綻放，讓我的心跳加速。"

    jump chapter2_end

## 選擇分支：提供幫助
label choice2_help:

    p "如果有什麼需要幫忙的，隨時可以找我。"

    p "修理東西、搬重物，或者只是想找人聊天都可以。"

    show misaki gentle
    with dissolve

    m "你真的很溫柔...謝謝你。"

    "她的眼中閃爍著感激的光芒。"

    p "鄰居之間互相照應是應該的。"

    show misaki shy
    with dissolve

    m "其實...我家的水龍頭最近有點問題。"

    m "如果你方便的話..."

    p "當然沒問題，我現在就可以去看看。"

    scene bg_misaki_apartment
    with fade

    "我跟著美咲來到她的公寓。"

    "這是我第一次進入她的私人空間。"

    "房間很整潔，但能感受到一種淡淡的寂寞氛圍。"

    show misaki normal
    with dissolve

    m "就是廚房的水龍頭，水流變得很小。"

    "我檢查了一下，發現是濾網堵塞的問題。"

    p "很簡單的問題，清理一下就好了。"

    "修理過程中，美咲在旁邊看著，偶爾遞給我工具。"

    "這種日常的互動讓我們之間的距離拉近了不少。"

    p "好了，試試看。"

    show misaki happy
    with dissolve

    m "哇，水流恢復正常了！真的很謝謝你。"

    m "要不要喝杯茶再走？"

    "她的邀請讓我感到溫暖。"

    p "好啊，謝謝。"

    "我們坐在客廳裡喝茶聊天，氣氛很溫馨。"

    jump chapter2_end

## 選擇分支：保持現狀
label choice2_neutral:

    p "嗯，生活確實不容易。"

    "我選擇不多說什麼，保持適當的距離。"

    show misaki normal
    with dissolve

    m "是啊...不過還是要繼續生活下去。"

    "她點點頭，臉上帶著堅強的笑容。"

    "雖然我們沒有進一步的交流，但這次幫忙讓我們彼此有了更多的了解。"

    p "那我先回去了，有事再找我。"

    m "好的，今天真的很謝謝你。"

    "我們禮貌地道別。"

    "雖然保持了距離，但我能感覺到她對我的好感有所增加。"

    jump chapter2_end

## 第二章結束
label chapter2_end:

    scene black
    with fade

    "第二章結束"

    "好感度：[affection]"

    pause 2.0

    ## 繼續到第三章
    jump chapter3

## 第三章：情感升溫
label chapter3:

    $ chapter = 3

    scene black
    with fade

    centered "{size=+10}第三章：情感升溫{/size}"

    pause 2.0

    "又過了幾天..."

    if affection >= 5:
        "美咲和我的關係變得更加親密。"
        "她開始主動找我聊天，我們經常一起度過傍晚時光。"
    elif affection >= 3:
        "美咲對我的信任逐漸加深。"
        "雖然她還是有些害羞，但已經願意和我分享更多心事。"
    else:
        "雖然我們的互動不多，但我能感覺到她對我的好感。"

    scene bg_hallway
    with fade

    "今晚，我聽到隔壁傳來輕微的哭泣聲。"

    "不是那種痛苦的哭聲，而是一種壓抑的、寂寞的啜泣。"

    "我猶豫了一下，最終還是決定去關心她。"

    "咚咚咚..."

    "我輕敲她的門。"

    "過了一會兒，門開了。"

    show misaki sad
    with dissolve

    "美咲的眼睛有些紅腫，顯然剛剛哭過。"

    m "啊...不好意思，我..."

    p "發生什麼事了嗎？"

    "我溫柔地問道，心中充滿了關切。"

    show misaki crying
    with dissolve

    m "今天...今天是我先生的忌日。"

    m "我以為自己已經習慣了，但是..."

    "她的聲音顫抖著，眼淚再次湧出。"

    p "可以讓我進去嗎？妳不應該一個人承受這些。"

    "她點點頭，讓我進入她的公寓。"

    scene bg_misaki_living_room
    with fade

    show misaki sad
    with dissolve

    "客廳的茶几上放著一張照片，應該是她和已故丈夫的合影。"

    "旁邊還有一束白色的花。"

    m "我們結婚五年...他生病的最後一年，我一直在照顧他。"

    m "他走的時候，我以為自己的世界也結束了。"

    "她坐在沙發上，雙手緊握著。"

    p "妳一定很愛他。"

    show misaki gentle
    with dissolve

    m "是的...但是現在，我開始感到困惑。"

    m "我...我開始對其他人產生了感情。"

    "她看向我，眼中帶著複雜的情感。"

    m "這樣是不是背叛了他？"

    ## 關鍵選擇點
    menu:
        "愛一個人不代表不能再愛別人":
            $ affection += 3
            jump choice3_love

        "妳有權利追求自己的幸福":
            $ affection += 2
            jump choice3_happiness

        "時間會治癒一切傷痛":
            $ affection += 1
            jump choice3_time
