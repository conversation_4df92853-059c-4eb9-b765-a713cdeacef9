## 主劇本檔案
## 隔牆的溫柔 - Tenderness Beyond the Wall

## 角色定義
define m = Character("美咲", color="#c8ffc8")
define p = Character("你", color="#c8c8ff")
define narrator = Character(None, kind=nvl)

## 變數初始化
default affection = 0
default chapter = 1
default ending_route = ""

## 遊戲開始
label start:
    
    ## 成人內容警告
    scene black
    with fade
    
    centered "{size=+10}{color=#ff6666}成人內容警告{/color}{/size}\n\n本遊戲包含成人內容，僅適合18歲以上玩家。\n\n請確認您已年滿18歲。"
    
    menu:
        "我已年滿18歲，繼續遊戲":
            pass
        "我未滿18歲，退出遊戲":
            return
    
    ## 遊戲標題畫面
    scene black
    with fade
    
    centered "{size=+20}{color=#ffc8c8}隔牆的溫柔{/color}{/size}\n{size=+5}Tenderness Beyond the Wall{/size}\n\n{size=-5}製作：紫暮工坊 Twilight Purple Studio{/size}"
    
    pause 3.0
    
    ## 開始第一章
    jump chapter1

## 第一章：深夜的邂逅
label chapter1:
    
    $ chapter = 1
    
    scene black
    with fade
    
    centered "{size=+10}第一章：深夜的邂逅{/size}"
    
    pause 2.0
    
    ## 場景：男主角公寓臥室
    scene bg_male_bedroom
    with fade
    
    "深夜2點15分。"
    
    "我被一陣微弱的聲音驚醒。"
    
    "起初以為是電視的聲音，但仔細聆聽後..."
    
    "那似乎是...女性的呻吟聲？"
    
    "聲音從隔壁傳來，時斷時續，帶著某種壓抑的情感。"
    
    p "這個時間...隔壁在做什麼？"
    
    p "聽起來像是...不，應該不會吧。"
    
    p "但是這聲音..."
    
    ## 第一個重要選擇
    menu:
        "敲門關心":
            $ affection += 2
            jump choice1_knock
        
        "默默聆聽":
            $ affection += 1
            jump choice1_listen
        
        "戴上耳機睡覺":
            $ affection -= 1
            jump choice1_ignore

## 選擇分支：敲門關心
label choice1_knock:
    
    "我決定去關心一下鄰居的狀況。"
    
    "也許她真的遇到了什麼困難。"
    
    scene bg_hallway
    with fade
    
    "我輕輕走到隔壁門前，猶豫了一下。"
    
    "然後輕敲了幾下門。"
    
    "咚咚咚..."
    
    "聲音突然停止了。"
    
    "過了一會兒，門內傳來慌張的腳步聲。"
    
    "門開了一條縫。"
    
    show misaki embarrassed
    with dissolve
    
    "一個美麗的女性出現在門後。"
    
    "她穿著薄薄的睡衣，臉頰還帶著紅暈，頭髮微亂。"
    
    "這就是我的鄰居...美咲。"
    
    m "啊...不好意思，這麼晚了..."
    
    p "不好意思這麼晚打擾，我聽到一些聲音，擔心妳是不是不舒服？"
    
    show misaki shy
    with dissolve
    
    m "我...我只是...看電視看得太入神了。"
    
    m "謝謝你的關心。"
    
    "她的聲音有些顫抖，眼神閃躲著不敢直視我。"
    
    "但我能感受到她眼中的溫柔。"
    
    p "如果有什麼需要幫忙的，隨時可以找我。"
    
    show misaki normal
    with dissolve
    
    m "謝謝...真的很謝謝你。"
    
    "她輕輕關上了門。"
    
    "但我能感覺到，這次相遇改變了什麼。"
    
    jump chapter1_end

## 選擇分支：默默聆聽
label choice1_listen:
    
    "我決定靜靜聆聽。"
    
    "也許...這是她的私人時間。"
    
    "聲音繼續傳來，帶著某種孤獨和渴望。"
    
    "我能感受到隔牆另一邊的寂寞。"
    
    "漸漸地，聲音平息了。"
    
    "我也重新躺回床上，但已經無法入睡。"
    
    "腦海中不斷想著那個聲音的主人。"
    
    jump chapter1_end

## 選擇分支：戴上耳機
label choice1_ignore:
    
    "我決定不去理會。"
    
    "每個人都有自己的隱私。"
    
    "我戴上耳機，試圖重新入睡。"
    
    "但那個聲音似乎已經深深印在我的記憶中。"
    
    jump chapter1_end

## 第一章結束
label chapter1_end:
    
    scene black
    with fade
    
    "第一章結束"
    
    "好感度：[affection]"
    
    pause 2.0
    
    ## 繼續到第二章
    jump chapter2

## 第二章：逐漸親近
label chapter2:
    
    $ chapter = 2
    
    scene black
    with fade
    
    centered "{size=+10}第二章：逐漸親近{/size}"
    
    pause 2.0
    
    "一週後..."
    
    scene bg_hallway
    with fade
    
    "我和美咲的互動開始增加。"
    
    if affection >= 2:
        "自從那晚的相遇後，她對我似乎更加信任。"
    else:
        "雖然我們之前沒有直接接觸，但命運讓我們再次相遇。"
    
    show misaki normal
    with dissolve
    
    "今天我看到她在搬重物。"
    
    p "需要幫忙嗎？"
    
    show misaki shy
    with dissolve
    
    m "啊...如果不麻煩的話..."
    
    "我幫她搬運了一些箱子。"
    
    "在這個過程中，我們開始了更多的對話。"
    
    show misaki normal
    with dissolve
    
    m "真的很謝謝你。自從我先生過世後，很少有人這樣幫助我。"
    
    p "鄰居互相幫忙是應該的，而且...妳一個人住一定很辛苦。"
    
    show misaki sad
    with dissolve
    
    m "是啊...有時候真的很寂寞。"
    
    "她的眼中閃過一絲哀傷。"
    
    "但很快又恢復了溫和的笑容。"
    
    ## 第二個重要選擇
    menu:
        "主動邀請她吃晚餐":
            $ affection += 3
            jump choice2_dinner
        
        "暗示可以隨時找自己幫忙":
            $ affection += 2
            jump choice2_help
        
        "保持現狀，不多說什麼":
            $ affection += 1
            jump choice2_neutral

## 待續...
label choice2_dinner:
    "未完待續..."
    return

label choice2_help:
    "未完待續..."
    return

label choice2_neutral:
    "未完待續..."
    return
