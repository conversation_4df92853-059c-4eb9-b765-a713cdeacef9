# 簡化的模型下載腳本
Write-Host "=== Stable Diffusion 模型下載 ===" -ForegroundColor Green

$modelDir = "D:\Delta\tools\stable-diffusion-webui\models\Stable-diffusion"

# 檢查目錄是否存在
if (!(Test-Path $modelDir)) {
    Write-Host "錯誤: 模型目錄不存在 $modelDir" -ForegroundColor Red
    exit 1
}

Write-Host "模型將下載到: $modelDir" -ForegroundColor Cyan

# 模型1: Anything V5 (最重要)
$model1Name = "anything-v5-PrtRE.safetensors"
$model1Path = Join-Path $modelDir $model1Name

if (!(Test-Path $model1Path)) {
    Write-Host "`n正在下載 Anything V5..." -ForegroundColor Yellow
    try {
        $url1 = "https://huggingface.co/stablediffusionapi/anything-v5/resolve/main/anything-v5-PrtRE.safetensors"
        Invoke-WebRequest -Uri $url1 -OutFile $model1Path -UseBasicParsing
        Write-Host "✓ Anything V5 下載完成" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Anything V5 下載失敗: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Anything V5 已存在" -ForegroundColor Cyan
}

# 模型2: ChilloutMix
$model2Name = "chilloutmix_NiPrunedFp32Fix.safetensors"
$model2Path = Join-Path $modelDir $model2Name

if (!(Test-Path $model2Path)) {
    Write-Host "`n正在下載 ChilloutMix..." -ForegroundColor Yellow
    try {
        $url2 = "https://huggingface.co/TASUKU2023/Chilloutmix/resolve/main/chilloutmix_NiPrunedFp32Fix.safetensors"
        Invoke-WebRequest -Uri $url2 -OutFile $model2Path -UseBasicParsing
        Write-Host "✓ ChilloutMix 下載完成" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ ChilloutMix 下載失敗: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "ChilloutMix 已存在" -ForegroundColor Cyan
}

Write-Host "`n=== 下載完成 ===" -ForegroundColor Green
Write-Host "請重啟 Stable Diffusion WebUI 以載入新模型" -ForegroundColor Yellow

# 列出所有模型
Write-Host "`n當前可用模型:" -ForegroundColor Magenta
Get-ChildItem $modelDir -Filter "*.safetensors" | ForEach-Object {
    Write-Host "- $($_.Name)" -ForegroundColor White
}
