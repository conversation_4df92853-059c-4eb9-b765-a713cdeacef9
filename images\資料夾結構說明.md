# 圖片資料夾結構說明
## 隔牆的溫柔 - 圖片資源組織

---

## 資料夾結構概覽

```
images/
├── characters/           # 角色立繪
│   ├── misaki/          # 女主角美咲
│   │   ├── expressions/ # 表情變化
│   │   └── outfits/     # 服裝變化
│   └── male_protagonist/ # 男主角剪影
├── backgrounds/         # 背景場景
├── cg/                 # CG場景圖片
│   ├── daily/          # 日常互動CG
│   └── intimate/       # 親密場景CG
└── ui/                 # 使用者介面素材
```

---

## 各資料夾用途說明

### 📁 characters/ - 角色立繪
存放角色的基本立繪和變化圖片

#### 📁 characters/misaki/ - 女主角美咲
- **用途**：美咲的所有立繪變化
- **命名規則**：`misaki_[狀態]_[編號].png`
- **建議解析度**：1024x1024 或 768x1024

##### 📁 expressions/ - 表情變化
- `misaki_normal_01.png` - 日常表情
- `misaki_shy_01.png` - 害羞表情
- `misaki_lustful_01.png` - 慾望表情
- `misaki_satisfied_01.png` - 滿足表情

##### 📁 outfits/ - 服裝變化
- `misaki_casual_01.png` - 日常居家服
- `misaki_nightgown_01.png` - 睡衣
- `misaki_nude_01.png` - 裸體
- `misaki_bathroom_01.png` - 浴室場景

#### 📁 characters/male_protagonist/ - 男主角
- **用途**：男主角剪影圖片
- **命名規則**：`male_silhouette_[動作]_[編號].png`
- **特色**：全黑剪影設計

### 📁 backgrounds/ - 背景場景
存放遊戲中的背景圖片

**主要場景**：
- `apartment_hallway.png` - 公寓走廊
- `misaki_bedroom.png` - 美咲臥室
- `misaki_living_room.png` - 美咲客廳
- `misaki_bathroom.png` - 美咲浴室
- `misaki_kitchen.png` - 美咲廚房
- `male_apartment.png` - 男主角公寓
- `rooftop_night.png` - 天台夜景

### 📁 cg/ - CG場景圖片
存放特殊場景的CG圖片

#### 📁 cg/daily/ - 日常互動CG
- **用途**：日常生活中的重要場景
- **數量**：預計8-10張
- **命名規則**：`daily_[場景描述]_[編號].png`

**場景範例**：
- `daily_first_meeting_01.png` - 初次相遇
- `daily_helping_repair_01.png` - 幫忙修理
- `daily_rooftop_talk_01.png` - 天台談心
- `daily_first_hug_01.png` - 第一次擁抱

#### 📁 cg/intimate/ - 親密場景CG
- **用途**：成人向親密場景
- **數量**：預計10-12張
- **命名規則**：`intimate_[場景描述]_[編號].png`
- **注意**：高品質成人內容

**場景範例**：
- `intimate_masturbation_01.png` - 自慰場景
- `intimate_first_kiss_01.png` - 初吻
- `intimate_foreplay_01.png` - 前戲
- `intimate_climax_01.png` - 高潮場景

### 📁 ui/ - 使用者介面素材
存放遊戲UI相關的圖片素材

**UI元素**：
- `textbox.png` - 對話框
- `choice_button.png` - 選擇按鈕
- `save_load_bg.png` - 存檔載入背景
- `main_menu_bg.png` - 主選單背景

---

## 圖片規格建議

### 解析度標準
- **角色立繪**：1024x1024 或 768x1024
- **背景圖片**：1920x1080
- **CG場景**：1920x1080 或 1600x900
- **UI素材**：依實際需求調整

### 檔案格式
- **主要格式**：PNG (支援透明背景)
- **備用格式**：JPG (背景圖片可用)
- **品質要求**：高品質，適合遊戲使用

### 命名規範
- 使用英文命名，避免中文字符
- 使用底線分隔，避免空格
- 包含版本編號便於管理
- 保持命名一致性

---

## 使用注意事項

### Stable Diffusion 生成
1. 參考「角色立繪提示詞.md」進行生成
2. 保持角色特徵一致性
3. 注意光影效果統一
4. 後製處理提升品質

### 檔案管理
1. 定期備份重要圖片
2. 保留原始生成檔案
3. 建立版本控制系統
4. 記錄生成參數設定

### Ren'Py 整合
1. 圖片路徑要與程式碼對應
2. 檔案大小控制在合理範圍
3. 測試不同解析度顯示效果
4. 優化載入速度

---

*資料夾結構版本：v1.0*
*建立日期：2025-07-27*
