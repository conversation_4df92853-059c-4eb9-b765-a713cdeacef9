# 使用 curl 下載 Stable Diffusion 模型
Write-Host "=== 使用 curl 下載模型 ===" -ForegroundColor Green

$modelDir = "D:\Delta\tools\stable-diffusion-webui\models\Stable-diffusion"

# 檢查 curl 是否可用
try {
    curl --version | Out-Null
    Write-Host "✓ curl 可用" -ForegroundColor Green
} catch {
    Write-Host "✗ curl 不可用，請使用手動下載" -ForegroundColor Red
    exit 1
}

# 模型下載列表 (使用直接下載連結)
$models = @(
    @{
        Name = "Anything V5"
        URL = "https://huggingface.co/Linaqruf/anything-v3.0/resolve/main/anything-v5-PrtRE.safetensors"
        FileName = "anything-v5-PrtRE.safetensors"
    },
    @{
        Name = "ChilloutMix"  
        URL = "https://huggingface.co/TASUKU2023/Chilloutmix/resolve/main/chilloutmix_NiPrunedFp32Fix.safetensors"
        FileName = "chilloutmix_NiPrunedFp32Fix.safetensors"
    }
)

foreach ($model in $models) {
    $filePath = Join-Path $modelDir $model.FileName
    
    if (Test-Path $filePath) {
        Write-Host "$($model.Name) 已存在，跳過下載" -ForegroundColor Cyan
        continue
    }
    
    Write-Host "`n正在下載 $($model.Name)..." -ForegroundColor Yellow
    Write-Host "URL: $($model.URL)" -ForegroundColor Gray
    
    try {
        # 使用 curl 下載，顯示進度
        $curlArgs = @(
            "-L",  # 跟隨重定向
            "-o", $filePath,  # 輸出檔案
            "--progress-bar",  # 顯示進度條
            $model.URL
        )
        
        & curl @curlArgs
        
        if (Test-Path $filePath) {
            $fileSize = (Get-Item $filePath).Length / 1GB
            Write-Host "✓ $($model.Name) 下載完成 (${fileSize:F2} GB)" -ForegroundColor Green
        } else {
            Write-Host "✗ $($model.Name) 下載失敗" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "✗ $($model.Name) 下載出錯: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 下載總結 ===" -ForegroundColor Magenta
Write-Host "當前可用模型:" -ForegroundColor Cyan
Get-ChildItem $modelDir -Filter "*.safetensors" | ForEach-Object {
    $size = $_.Length / 1GB
    Write-Host "- $($_.Name) (${size:F2} GB)" -ForegroundColor White
}

Write-Host "`n請重啟 Stable Diffusion WebUI 以載入新模型" -ForegroundColor Yellow
