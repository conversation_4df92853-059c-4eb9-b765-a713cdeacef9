# 重啟 Stable Diffusion WebUI 並載入新模型
Write-Host "=== 重啟 Stable Diffusion WebUI ===" -ForegroundColor Green

$webuiDir = "D:\Delta\tools\stable-diffusion-webui"
$modelDir = "$webuiDir\models\Stable-diffusion"

# 檢查WebUI目錄
if (!(Test-Path $webuiDir)) {
    Write-Host "❌ WebUI目錄不存在: $webuiDir" -ForegroundColor Red
    exit 1
}

# 檢查模型
Write-Host "`n=== 檢查可用模型 ===" -ForegroundColor Cyan
$models = Get-ChildItem $modelDir -Filter "*.safetensors"
foreach ($model in $models) {
    $sizeGB = [math]::Round($model.Length / 1GB, 2)
    Write-Host "✅ $($model.Name) - ${sizeGB} GB" -ForegroundColor White
}

Write-Host "`n=== 停止現有進程 ===" -ForegroundColor Yellow
# 嘗試優雅地停止WebUI進程
$webuiProcesses = Get-Process | Where-Object {
    $_.ProcessName -like "*webui*" -or 
    ($_.ProcessName -eq "python" -and $_.MainWindowTitle -like "*Stable Diffusion*")
}

if ($webuiProcesses) {
    Write-Host "找到 $($webuiProcesses.Count) 個WebUI相關進程" -ForegroundColor Yellow
    foreach ($proc in $webuiProcesses) {
        try {
            $proc.CloseMainWindow()
            Start-Sleep -Seconds 2
            if (!$proc.HasExited) {
                $proc.Kill()
            }
            Write-Host "✅ 已停止進程: $($proc.ProcessName) (ID: $($proc.Id))" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠️ 無法停止進程: $($proc.ProcessName) (ID: $($proc.Id))" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "沒有找到運行中的WebUI進程" -ForegroundColor Cyan
}

Write-Host "`n=== 啟動WebUI ===" -ForegroundColor Green
Write-Host "正在啟動 Stable Diffusion WebUI..." -ForegroundColor Yellow
Write-Host "請等待WebUI完全載入後再進行測試" -ForegroundColor Yellow

# 切換到WebUI目錄並啟動
Set-Location $webuiDir

# 啟動WebUI（在新窗口中）
Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "webui-user.bat" -WorkingDirectory $webuiDir

Write-Host "`n=== 啟動完成 ===" -ForegroundColor Green
Write-Host "WebUI正在啟動中，請稍等片刻..." -ForegroundColor Cyan
Write-Host "啟動完成後，請訪問: http://127.0.0.1:7860" -ForegroundColor White
Write-Host "`n預期可用模型:" -ForegroundColor Magenta
foreach ($model in $models) {
    $modelName = $model.Name -replace '\.safetensors$', ''
    Write-Host "- $modelName" -ForegroundColor White
}

Write-Host "`n⚠️ 注意事項:" -ForegroundColor Yellow
Write-Host "1. 如果下載了AnythingXL，那是SDXL模型，需要特殊處理" -ForegroundColor Yellow
Write-Host "2. 建議下載標準的Anything V5 (SD 1.5版本)" -ForegroundColor Yellow
Write-Host "3. WebUI啟動後，在模型選擇中確認新模型出現" -ForegroundColor Yellow
